<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Traductions en Direct</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
        .lang-btn { margin: 10px; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; }
        .lang-btn.active { background-color: #007cba; color: white; }
        .lang-btn:not(.active) { background-color: #e9ecef; color: #333; }
        .category-tab { margin: 5px; padding: 15px; border: 1px solid #ddd; border-radius: 5px; background: #f8f9fa; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }
        .debug-info { background: #f8f9fa; padding: 10px; border-radius: 3px; font-family: monospace; font-size: 12px; }
        .success { color: #28a745; }
        .error { color: #dc3545; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Test Traductions en Direct</h1>
        
        <div class="language-switch">
            <button class="lang-btn active" data-lang="ar">العربية (AR)</button>
            <button class="lang-btn" data-lang="en">English (EN)</button>
        </div>

        <div class="test-section">
            <h2>Catégories depuis la Base de Données</h2>
            <div class="category-tabs" id="category-tabs">
                <?php
                try {
                    require_once 'db_connect.php';
                    $db = Database::getInstance()->getConnection();
                    $categories = $db->query("SELECT id, name_en, name_ar FROM categories ORDER BY id")->fetchAll();
                    
                    foreach ($categories as $category) {
                        $translation_key = strtolower(str_replace([' & ', ' '], ['_', '_'], $category['name_en']));
                        echo '<div class="category-tab" data-i18n="categories.' . $translation_key . '" data-name-ar="' . htmlspecialchars($category['name_ar']) . '">';
                        echo htmlspecialchars($category['name_en']);
                        echo '</div>';
                    }
                } catch (Exception $e) {
                    echo '<div class="error">Erreur: ' . $e->getMessage() . '</div>';
                }
                ?>
            </div>
        </div>

        <div class="test-section">
            <h2>Navigation (Traductions JavaScript)</h2>
            <nav>
                <span data-i18n="nav.home">Home</span> |
                <span data-i18n="nav.categories">Categories</span> |
                <span data-i18n="nav.products">Products</span> |
                <span data-i18n="nav.about">About Us</span> |
                <span data-i18n="nav.contact">Contact</span>
            </nav>
        </div>

        <div class="test-section">
            <h2>🔍 Informations de Debug</h2>
            <div class="debug-info" id="debug-info">
                <strong>Langue actuelle:</strong> <span id="current-lang">ar</span><br>
                <strong>Direction:</strong> <span id="current-dir">rtl</span><br>
                <strong>Encodage de la page:</strong> UTF-8<br>
                <strong>User Agent:</strong> <span id="user-agent"></span><br>
                <strong>Dernière action:</strong> <span id="last-action">Chargement initial</span>
            </div>
        </div>

        <div class="test-section">
            <h2>📊 Test des Traductions</h2>
            <div id="translation-test-results"></div>
        </div>
    </div>

    <script>
        // Current language state
        let currentLang = 'ar';

        // Translation data (same as in script.js)
        const translations = {
            en: {
                'nav.home': 'Home',
                'nav.categories': 'Categories',
                'nav.products': 'Products',
                'nav.about': 'About Us',
                'nav.contact': 'Contact',
                'categories.lighters_gas': 'Lighters & Gas',
                'categories.toothbrushes': 'Toothbrushes',
                'categories.adhesive_glue': 'Adhesive Glue',
                'categories.baby_products': 'Baby Products',
                'categories.insecticides': 'Insecticides'
            },
            ar: {
                'nav.home': 'الرئيسية',
                'nav.categories': 'الفئات',
                'nav.products': 'المنتجات',
                'nav.about': 'من نحن',
                'nav.contact': 'اتصل بنا',
                'categories.lighters_gas': 'الولاعات والغاز',
                'categories.toothbrushes': 'فرش الأسنان',
                'categories.adhesive_glue': 'الغراء اللاصق',
                'categories.baby_products': 'منتجات الأطفال',
                'categories.insecticides': 'المبيدات الحشرية'
            }
        };

        // Language switching functionality
        function handleLanguageSwitch(e) {
            const newLang = e.target.dataset.lang;
            if (!newLang || newLang === currentLang) return;
            
            currentLang = newLang;
            updateLanguage(newLang);
            updateActiveLanguageButton(e.target);
            updateDebugInfo('Changement de langue vers: ' + newLang);
        }

        function updateLanguage(lang) {
            console.log('Updating language to:', lang);
            
            document.querySelectorAll('[data-i18n]').forEach(element => {
                const key = element.dataset.i18n;
                console.log('Processing element with key:', key);
                
                // Pour les éléments avec data-name-ar, utiliser la traduction arabe directement
                if (lang === 'ar' && element.dataset.nameAr) {
                    console.log('Using data-name-ar:', element.dataset.nameAr);
                    element.textContent = element.dataset.nameAr;
                } else {
                    const translation = translations[lang] && translations[lang][key];
                    console.log('Using translation:', translation);
                    element.textContent = translation || element.textContent;
                }
            });

            // Update document language and direction
            document.documentElement.lang = lang;
            document.documentElement.dir = lang === 'ar' ? 'rtl' : 'ltr';
            
            // Update debug info
            document.getElementById('current-lang').textContent = lang;
            document.getElementById('current-dir').textContent = lang === 'ar' ? 'rtl' : 'ltr';
        }

        function updateActiveLanguageButton(activeBtn) {
            document.querySelectorAll('.lang-btn').forEach(btn => {
                btn.classList.toggle('active', btn === activeBtn);
            });
        }

        function updateDebugInfo(action) {
            document.getElementById('last-action').textContent = action + ' à ' + new Date().toLocaleTimeString();
        }

        function runTranslationTests() {
            const results = [];
            
            // Test 1: Vérifier que les traductions existent
            for (const lang in translations) {
                for (const key in translations[lang]) {
                    if (translations[lang][key]) {
                        results.push(`✅ ${lang}.${key}: "${translations[lang][key]}"`);
                    } else {
                        results.push(`❌ ${lang}.${key}: MANQUANT`);
                    }
                }
            }
            
            // Test 2: Vérifier les éléments data-name-ar
            const elementsWithArabic = document.querySelectorAll('[data-name-ar]');
            results.push(`\n📊 Éléments avec data-name-ar: ${elementsWithArabic.length}`);
            
            elementsWithArabic.forEach((el, index) => {
                const arabicText = el.dataset.nameAr;
                if (arabicText && arabicText.trim()) {
                    results.push(`✅ Élément ${index + 1}: "${arabicText}"`);
                } else {
                    results.push(`❌ Élément ${index + 1}: data-name-ar vide`);
                }
            });
            
            document.getElementById('translation-test-results').innerHTML = 
                '<pre>' + results.join('\n') + '</pre>';
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            // Add event listeners to language buttons
            document.querySelectorAll('.lang-btn').forEach(btn => {
                btn.addEventListener('click', handleLanguageSwitch);
            });

            // Set initial language
            updateLanguage(currentLang);
            
            // Update user agent
            document.getElementById('user-agent').textContent = navigator.userAgent;
            
            // Run translation tests
            runTranslationTests();
            
            updateDebugInfo('Page chargée');
        });
    </script>
</body>
</html>
