# 🚀 Guide de Déploiement cPanel - Traductions

## 📋 Problème Identifié

Les traductions fonctionnent en local mais ne s'affichent pas correctement sur cPanel après déploiement.

## 🔍 Causes Possibles

1. **Cache du navigateur** - Les anciens fichiers sont mis en cache
2. **Configuration serveur** - Encodage UTF-8 non configuré
3. **Permissions de fichiers** - Dossier cache non accessible
4. **Configuration PHP** - Paramètres UTF-8 différents
5. **Fichiers non synchronisés** - Certains fichiers n'ont pas été uploadés

## ✅ Solutions Appliquées

### 1. Scripts de Diagnostic et Correction

Trois nouveaux scripts ont été créés pour diagnostiquer et corriger les problèmes :

- **`diagnostic_cpanel.php`** - Diagnostic complet du système
- **`fix_cpanel_translations.php`** - Correction automatique des problèmes
- **`test_translation_live.php`** - Test en direct du système de traduction

### 2. Configuration Serveur (.htaccess)

Un fichier `.htaccess` optimisé a été créé avec :
- Configuration UTF-8 forcée
- Headers HTTP appropriés
- Types MIME corrects
- Configuration PHP UTF-8

### 3. Versioning des Fichiers Statiques

Les fichiers CSS et JS ont été versionnés pour forcer le rechargement :
- `styles.css?v=1.1`
- `script.js?v=1.1`

## 📝 Instructions de Déploiement

### Étape 1: Upload des Fichiers
1. Uploadez TOUS les fichiers sur cPanel via le gestionnaire de fichiers
2. Assurez-vous que ces fichiers sont présents :
   - `index.php` (mis à jour)
   - `script.js`
   - `styles.css`
   - `db_connect.php`
   - `.htaccess` (nouveau)
   - `diagnostic_cpanel.php` (nouveau)
   - `fix_cpanel_translations.php` (nouveau)
   - `test_translation_live.php` (nouveau)

### Étape 2: Configuration de la Base de Données
1. Vérifiez que la base de données `madjour_industries` existe
2. Assurez-vous que les tables `categories` et `products` contiennent les données
3. Vérifiez que les colonnes `name_ar` contiennent les traductions arabes

### Étape 3: Exécution des Scripts de Correction
1. Accédez à `votre-domaine.com/diagnostic_cpanel.php`
2. Vérifiez tous les tests (ils doivent être verts ✅)
3. Si des erreurs apparaissent, exécutez `votre-domaine.com/fix_cpanel_translations.php`
4. Testez avec `votre-domaine.com/test_translation_live.php`

### Étape 4: Configuration cPanel (si nécessaire)
Si les problèmes persistent, vérifiez dans cPanel :
1. **PHP Version** - Utilisez PHP 7.4 ou supérieur
2. **Extensions PHP** - Vérifiez que `mbstring` et `PDO` sont activées
3. **Limites PHP** - Augmentez `memory_limit` si nécessaire

## 🧪 Tests de Validation

### Test 1: Diagnostic Complet
```
URL: votre-domaine.com/diagnostic_cpanel.php
Résultat attendu: Tous les tests en vert ✅
```

### Test 2: Traductions en Direct
```
URL: votre-domaine.com/test_translation_live.php
Actions: Cliquer sur EN/AR et vérifier les changements
Résultat attendu: Traductions correctes dans les deux langues
```

### Test 3: Site Principal
```
URL: votre-domaine.com/index.php
Actions: Cliquer sur les boutons de langue
Résultat attendu: 
- Catégories en arabe depuis la base de données
- Navigation en arabe depuis JavaScript
- Direction RTL/LTR correcte
```

## 🔧 Dépannage

### Problème: Cache du Navigateur
**Solution**: Vider le cache (Ctrl+F5) ou utiliser mode incognito

### Problème: Erreur de Base de Données
**Solution**: 
1. Vérifier les identifiants dans `db_connect.php`
2. Exécuter `diagnostic_cpanel.php` pour voir l'erreur exacte
3. Contacter l'hébergeur si nécessaire

### Problème: Caractères Arabes Corrompus
**Solution**:
1. Vérifier que le fichier `.htaccess` est présent
2. Exécuter `fix_cpanel_translations.php`
3. Vérifier la configuration UTF-8 du serveur

### Problème: JavaScript ne Fonctionne Pas
**Solution**:
1. Vérifier la console du navigateur (F12)
2. S'assurer que `script.js` est accessible
3. Vérifier les erreurs dans les logs cPanel

## 📊 Fichiers Modifiés/Créés

### Fichiers Modifiés
- `index.php` - Ajout de `category_name_ar` dans la requête SQL + versioning
- `.htaccess` - Configuration UTF-8 et headers

### Nouveaux Fichiers
- `diagnostic_cpanel.php` - Script de diagnostic
- `fix_cpanel_translations.php` - Script de correction
- `test_translation_live.php` - Test en direct
- `GUIDE_DEPLOIEMENT_CPANEL.md` - Ce guide

## 🎯 Points de Contrôle Final

Avant de considérer le déploiement comme réussi, vérifiez :

- [ ] Tous les fichiers sont uploadés sur cPanel
- [ ] `diagnostic_cpanel.php` affiche tous les tests en vert
- [ ] `test_translation_live.php` fonctionne correctement
- [ ] Le site principal (`index.php`) affiche les traductions
- [ ] Les boutons de langue changent correctement le contenu
- [ ] Les caractères arabes s'affichent correctement
- [ ] La direction RTL/LTR fonctionne

## 📞 Support

Si les problèmes persistent après avoir suivi ce guide :

1. Exécutez `diagnostic_cpanel.php` et notez les erreurs
2. Vérifiez les logs d'erreur dans cPanel
3. Contactez votre hébergeur avec les détails techniques
4. Fournissez les résultats du diagnostic pour un support plus rapide

---

**Version**: 1.0  
**Date**: 2025-07-29  
**Auteur**: Assistant IA Augment
