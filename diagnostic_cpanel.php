<?php
// diagnostic_cpanel.php - Script de diagnostic pour les problèmes de traduction sur cPanel
header('Content-Type: text/html; charset=UTF-8');
?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Diagnostic cPanel - Traductions</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background: #d4edda; border-color: #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border-color: #f5c6cb; color: #721c24; }
        .warning { background: #fff3cd; border-color: #ffeaa7; color: #856404; }
        .info { background: #d1ecf1; border-color: #bee5eb; color: #0c5460; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
        .test-btn { background: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin: 5px; }
        .test-btn:hover { background: #005a8b; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Diagnostic cPanel - Problèmes de Traduction</h1>
        
        <?php
        echo "<div class='section info'>";
        echo "<h2>📊 Informations Serveur</h2>";
        echo "<strong>PHP Version:</strong> " . phpversion() . "<br>";
        echo "<strong>Server Software:</strong> " . ($_SERVER['SERVER_SOFTWARE'] ?? 'Non disponible') . "<br>";
        echo "<strong>Document Root:</strong> " . ($_SERVER['DOCUMENT_ROOT'] ?? 'Non disponible') . "<br>";
        echo "<strong>Script Path:</strong> " . __FILE__ . "<br>";
        echo "<strong>Current Time:</strong> " . date('Y-m-d H:i:s') . "<br>";
        echo "</div>";

        // Test 1: Connexion à la base de données
        echo "<div class='section'>";
        echo "<h2>🗄️ Test 1: Connexion Base de Données</h2>";
        try {
            require_once 'db_connect.php';
            $db = Database::getInstance()->getConnection();
            echo "<div class='success'>✅ Connexion à la base de données réussie</div>";
            
            // Test des catégories
            $categories = $db->query("SELECT id, name_en, name_ar FROM categories ORDER BY id")->fetchAll();
            echo "<div class='success'>✅ Récupération des catégories réussie (" . count($categories) . " catégories)</div>";
            
            echo "<h3>Catégories dans la base:</h3>";
            echo "<pre>";
            foreach ($categories as $cat) {
                echo "ID: {$cat['id']} | EN: {$cat['name_en']} | AR: {$cat['name_ar']}\n";
            }
            echo "</pre>";
            
        } catch (Exception $e) {
            echo "<div class='error'>❌ Erreur de connexion: " . $e->getMessage() . "</div>";
        }
        echo "</div>";

        // Test 2: Fichiers CSS et JS
        echo "<div class='section'>";
        echo "<h2>📁 Test 2: Fichiers Statiques</h2>";
        
        $files_to_check = ['styles.css', 'script.js'];
        foreach ($files_to_check as $file) {
            if (file_exists($file)) {
                $size = filesize($file);
                $modified = date('Y-m-d H:i:s', filemtime($file));
                echo "<div class='success'>✅ {$file} existe (Taille: {$size} bytes, Modifié: {$modified})</div>";
            } else {
                echo "<div class='error'>❌ {$file} introuvable</div>";
            }
        }
        echo "</div>";

        // Test 3: Cache et permissions
        echo "<div class='section'>";
        echo "<h2>🔒 Test 3: Cache et Permissions</h2>";
        
        // Vérifier le dossier cache
        if (is_dir('cache')) {
            echo "<div class='success'>✅ Dossier cache existe</div>";
            if (is_writable('cache')) {
                echo "<div class='success'>✅ Dossier cache accessible en écriture</div>";
            } else {
                echo "<div class='error'>❌ Dossier cache non accessible en écriture</div>";
            }
            
            // Vérifier le fichier de cache des traductions
            if (file_exists('cache/translations.json')) {
                $cache_age = time() - filemtime('cache/translations.json');
                echo "<div class='info'>ℹ️ Cache des traductions existe (Age: {$cache_age} secondes)</div>";
                
                // Afficher le contenu du cache
                $cache_content = file_get_contents('cache/translations.json');
                $cache_data = json_decode($cache_content, true);
                if ($cache_data) {
                    echo "<h3>Contenu du cache des traductions:</h3>";
                    echo "<pre>" . json_encode($cache_data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>";
                } else {
                    echo "<div class='error'>❌ Cache des traductions corrompu</div>";
                }
            } else {
                echo "<div class='warning'>⚠️ Cache des traductions n'existe pas</div>";
            }
        } else {
            echo "<div class='warning'>⚠️ Dossier cache n'existe pas</div>";
        }
        echo "</div>";

        // Test 4: Encodage UTF-8
        echo "<div class='section'>";
        echo "<h2>🔤 Test 4: Encodage UTF-8</h2>";
        
        $test_arabic = "الولاعات والغاز";
        echo "<strong>Test d'affichage arabe:</strong> {$test_arabic}<br>";
        echo "<strong>Encodage détecté:</strong> " . mb_detect_encoding($test_arabic) . "<br>";
        echo "<strong>Longueur UTF-8:</strong> " . mb_strlen($test_arabic, 'UTF-8') . " caractères<br>";
        
        // Test de la configuration PHP
        echo "<strong>default_charset:</strong> " . ini_get('default_charset') . "<br>";
        echo "<strong>mbstring.internal_encoding:</strong> " . ini_get('mbstring.internal_encoding') . "<br>";
        echo "</div>";

        // Test 5: Headers HTTP
        echo "<div class='section'>";
        echo "<h2>🌐 Test 5: Headers HTTP</h2>";
        echo "<pre>";
        foreach (getallheaders() as $name => $value) {
            echo "{$name}: {$value}\n";
        }
        echo "</pre>";
        echo "</div>";
        ?>

        <div class="section">
            <h2>🔧 Actions de Correction</h2>
            <button class="test-btn" onclick="clearCache()">Vider le Cache</button>
            <button class="test-btn" onclick="regenerateTranslations()">Régénérer les Traductions</button>
            <button class="test-btn" onclick="testTranslationSystem()">Tester le Système de Traduction</button>
            
            <div id="action-results"></div>
        </div>
    </div>

    <script>
        function clearCache() {
            fetch('diagnostic_cpanel.php?action=clear_cache')
                .then(response => response.text())
                .then(data => {
                    document.getElementById('action-results').innerHTML = '<div class="info">Cache vidé: ' + data + '</div>';
                    setTimeout(() => location.reload(), 2000);
                });
        }

        function regenerateTranslations() {
            fetch('translations.php')
                .then(response => response.text())
                .then(data => {
                    document.getElementById('action-results').innerHTML = '<div class="success">Traductions régénérées</div>';
                    setTimeout(() => location.reload(), 2000);
                });
        }

        function testTranslationSystem() {
            window.open('test_translation_live.php', '_blank');
        }
    </script>
</body>
</html>

<?php
// Actions AJAX
if (isset($_GET['action'])) {
    switch ($_GET['action']) {
        case 'clear_cache':
            if (file_exists('cache/translations.json')) {
                unlink('cache/translations.json');
                echo "Cache des traductions supprimé";
            } else {
                echo "Aucun cache à supprimer";
            }
            exit;
    }
}
?>
