<?php
// fix_cpanel_translations.php - Script de correction pour les problèmes de traduction sur cPanel
header('Content-Type: text/html; charset=UTF-8');

echo "<!DOCTYPE html>
<html lang='fr'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Correction Traductions cPanel</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
        .success { background: #d4edda; padding: 10px; border-radius: 5px; color: #155724; margin: 10px 0; }
        .error { background: #f8d7da; padding: 10px; border-radius: 5px; color: #721c24; margin: 10px 0; }
        .warning { background: #fff3cd; padding: 10px; border-radius: 5px; color: #856404; margin: 10px 0; }
        .info { background: #d1ecf1; padding: 10px; border-radius: 5px; color: #0c5460; margin: 10px 0; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
    </style>
</head>
<body>
    <div class='container'>
        <h1>🔧 Correction des Traductions pour cPanel</h1>";

echo "<div class='info'>Début des corrections à " . date('Y-m-d H:i:s') . "</div>";

try {
    // Étape 1: Vérifier la connexion à la base de données
    echo "<h2>📊 Étape 1: Vérification de la base de données</h2>";
    require_once 'db_connect.php';
    $db = Database::getInstance()->getConnection();
    echo "<div class='success'>✅ Connexion à la base de données réussie</div>";
    
    // Vérifier les catégories
    $categories = $db->query("SELECT id, name_en, name_ar FROM categories ORDER BY id")->fetchAll();
    echo "<div class='success'>✅ " . count($categories) . " catégories trouvées</div>";
    
    // Afficher les catégories
    echo "<h3>Catégories actuelles:</h3><pre>";
    foreach ($categories as $cat) {
        echo "ID: {$cat['id']} | EN: {$cat['name_en']} | AR: {$cat['name_ar']}\n";
    }
    echo "</pre>";

    // Étape 2: Créer/vérifier le dossier cache
    echo "<h2>📁 Étape 2: Gestion du cache</h2>";
    if (!is_dir('cache')) {
        if (mkdir('cache', 0755, true)) {
            echo "<div class='success'>✅ Dossier cache créé</div>";
        } else {
            echo "<div class='error'>❌ Impossible de créer le dossier cache</div>";
        }
    } else {
        echo "<div class='success'>✅ Dossier cache existe</div>";
    }
    
    // Vérifier les permissions
    if (is_writable('cache')) {
        echo "<div class='success'>✅ Dossier cache accessible en écriture</div>";
    } else {
        echo "<div class='warning'>⚠️ Dossier cache non accessible en écriture - tentative de correction</div>";
        chmod('cache', 0755);
    }
    
    // Supprimer l'ancien cache
    if (file_exists('cache/translations.json')) {
        unlink('cache/translations.json');
        echo "<div class='info'>🗑️ Ancien cache supprimé</div>";
    }

    // Étape 3: Régénérer le cache des traductions
    echo "<h2>🔄 Étape 3: Régénération du cache des traductions</h2>";
    
    $translations = [
        'en' => [
            'nav.home' => 'Home',
            'nav.categories' => 'Categories',
            'nav.products' => 'Products',
            'nav.about' => 'About Us',
            'nav.contact' => 'Contact',
            'hero.title' => 'Trusted Products for Everyday Life',
            'hero.subtitle' => 'Madjour Industries Quality You Can Count On',
            'categories.title' => 'Categories',
            'products.title' => 'Our Products',
            'products.view_details' => 'View Details',
            'search.placeholder' => 'Search products...',
            'search.empty' => 'Please enter a search term.',
            'search.no_results' => 'No products found for "%s".',
            'about.title' => 'About EURL MADJOUR HICHEM',
            'about.description' => 'EURL MADJOUR HICHEM is a leading company in multiple sectors, comprising several manufacturing units, including lighters, gas, glue, insecticides, and semi-pharmaceutical materials.',
            'footer.quick_links' => 'Quick Links',
            'footer.contact_title' => 'Contact Info',
            'footer.rights' => 'All rights reserved.',
            'modal.close' => 'Close',
            'mobile.menu_toggle' => 'Toggle Menu'
        ],
        'ar' => [
            'nav.home' => 'الرئيسية',
            'nav.categories' => 'الفئات',
            'nav.products' => 'المنتجات',
            'nav.about' => 'من نحن',
            'nav.contact' => 'اتصل بنا',
            'hero.title' => 'منتجات موثوقة للحياة اليومية',
            'hero.subtitle' => 'صناعات مجور - جودة يمكنك الاعتماد عليها',
            'categories.title' => 'الفئات',
            'products.title' => 'منتجاتنا',
            'products.view_details' => 'عرض التفاصيل',
            'search.placeholder' => 'البحث عن المنتجات...',
            'search.empty' => 'يرجى إدخال مصطلح البحث.',
            'search.no_results' => 'لم يتم العثور على منتجات لـ "%s".',
            'about.title' => 'حول شركة EURL MADJOUR HICHEM',
            'about.description' => 'شركة EURL MADJOUR HICHEM هي شركة رائدة في عدة قطاعات، تضم عدة وحدات تصنيع، بما في ذلك الولاعات والغاز والغراء والمبيدات الحشرية والمواد شبه الصيدلانية.',
            'footer.quick_links' => 'روابط سريعة',
            'footer.contact_title' => 'معلومات الاتصال',
            'footer.rights' => 'جميع الحقوق محفوظة.',
            'modal.close' => 'إغلاق',
            'mobile.menu_toggle' => 'تبديل القائمة'
        ]
    ];
    
    // Ajouter les traductions des catégories depuis la base de données
    foreach ($categories as $category) {
        $key = strtolower(str_replace([' & ', ' '], ['_', '_'], $category['name_en']));
        $translations['en']['categories.' . $key] = $category['name_en'];
        $translations['ar']['categories.' . $key] = $category['name_ar'];
    }
    
    // Sauvegarder le cache
    $cache_content = json_encode($translations, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
    if (file_put_contents('cache/translations.json', $cache_content)) {
        echo "<div class='success'>✅ Cache des traductions créé avec succès</div>";
    } else {
        echo "<div class='error'>❌ Impossible de créer le cache des traductions</div>";
    }

    // Étape 4: Vérifier les fichiers CSS et JS
    echo "<h2>📄 Étape 4: Vérification des fichiers statiques</h2>";
    
    $files_to_check = [
        'styles.css' => 'Fichier CSS principal',
        'script.js' => 'Fichier JavaScript principal',
        'index.php' => 'Page principale'
    ];
    
    foreach ($files_to_check as $file => $description) {
        if (file_exists($file)) {
            $size = filesize($file);
            $modified = date('Y-m-d H:i:s', filemtime($file));
            echo "<div class='success'>✅ {$description} ({$file}) - Taille: {$size} bytes, Modifié: {$modified}</div>";
        } else {
            echo "<div class='error'>❌ {$description} ({$file}) introuvable</div>";
        }
    }

    // Étape 5: Créer un fichier .htaccess pour forcer UTF-8
    echo "<h2>🌐 Étape 5: Configuration serveur</h2>";
    
    $htaccess_content = "# Configuration UTF-8 pour les traductions
AddDefaultCharset UTF-8
AddCharset UTF-8 .php
AddCharset UTF-8 .html
AddCharset UTF-8 .css
AddCharset UTF-8 .js

# Headers pour le cache
<IfModule mod_headers.c>
    Header set Cache-Control \"no-cache, must-revalidate\" env=NO_CACHE
</IfModule>

# Configuration MIME types
<IfModule mod_mime.c>
    AddType text/html .php
    AddType application/javascript .js
    AddType text/css .css
</IfModule>";

    if (file_put_contents('.htaccess', $htaccess_content)) {
        echo "<div class='success'>✅ Fichier .htaccess créé/mis à jour</div>";
    } else {
        echo "<div class='warning'>⚠️ Impossible de créer le fichier .htaccess</div>";
    }

    // Étape 6: Test final
    echo "<h2>🧪 Étape 6: Test final</h2>";
    
    // Tester l'affichage des caractères arabes
    $test_arabic = "الولاعات والغاز";
    echo "<div class='info'>Test d'affichage arabe: <strong>{$test_arabic}</strong></div>";
    echo "<div class='info'>Encodage détecté: " . mb_detect_encoding($test_arabic) . "</div>";
    
    // Vérifier que le cache est lisible
    if (file_exists('cache/translations.json')) {
        $cache_test = json_decode(file_get_contents('cache/translations.json'), true);
        if ($cache_test && isset($cache_test['ar']['categories.lighters_gas'])) {
            echo "<div class='success'>✅ Cache des traductions fonctionnel</div>";
            echo "<div class='info'>Exemple: categories.lighters_gas = " . $cache_test['ar']['categories.lighters_gas'] . "</div>";
        } else {
            echo "<div class='error'>❌ Cache des traductions corrompu</div>";
        }
    }

    echo "<div class='success'>🎉 Corrections terminées avec succès!</div>";
    echo "<div class='info'>Vous pouvez maintenant tester votre site. Si les problèmes persistent, vérifiez les logs d'erreur de votre hébergeur.</div>";

} catch (Exception $e) {
    echo "<div class='error'>❌ Erreur lors des corrections: " . $e->getMessage() . "</div>";
    echo "<div class='info'>Détails de l'erreur: " . $e->getTraceAsString() . "</div>";
}

echo "<h2>📋 Actions recommandées après correction</h2>";
echo "<ol>
    <li>Vider le cache de votre navigateur (Ctrl+F5)</li>
    <li>Vérifier que tous les fichiers ont été uploadés sur cPanel</li>
    <li>Tester la page avec: <a href='test_translation_live.php' target='_blank'>test_translation_live.php</a></li>
    <li>Vérifier les logs d'erreur dans cPanel</li>
    <li>Si nécessaire, contacter votre hébergeur pour vérifier la configuration PHP</li>
</ol>";

echo "</div></body></html>";
?>
