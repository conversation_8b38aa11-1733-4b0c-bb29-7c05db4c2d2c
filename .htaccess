# Configuration UTF-8 pour les traductions
AddDefaultCharset UTF-8
AddCharset UTF-8 .php
AddCharset UTF-8 .html
AddCharset UTF-8 .css
AddCharset UTF-8 .js

# Headers pour le cache et UTF-8
<IfModule mod_headers.c>
    Header set Cache-Control "no-cache, must-revalidate" env=NO_CACHE
    Header always set Content-Type "text/html; charset=UTF-8"
    Header set X-Content-Type-Options nosniff
</IfModule>

# Configuration MIME types
<IfModule mod_mime.c>
    AddType text/html .php
    AddType application/javascript .js
    AddType text/css .css
    AddType application/json .json
</IfModule>

# Configuration PHP pour UTF-8 (si supporté par l'hébergeur)
<IfModule mod_php.c>
    php_value default_charset "UTF-8"
    php_value mbstring.internal_encoding "UTF-8"
    php_value mbstring.http_output "UTF-8"
</IfModule>

# Redirection d'erreur personnalisée
ErrorDocument 404 /index.php
ErrorDocument 500 /index.php
